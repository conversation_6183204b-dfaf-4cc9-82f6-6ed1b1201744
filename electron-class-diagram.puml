@startuml Electron应用类关系图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam classBackgroundColor #F8F9FA
skinparam classBorderColor #6C757D
skinparam classArrowColor #495057
skinparam packageBackgroundColor #E9ECEF
skinparam packageBorderColor #ADB5BD
skinparam stereotypeCBackgroundColor #FFF3CD
skinparam stereotypeCBorderColor #FFEAA7

title Electron应用类关系图 - 从electron-client入口开始

' 定义包和进程边界
package "主进程 (Main Process)" as MainProcess {

    ' 主进程入口
    class "main/index.ts" as MainEntry {
        +app: Electron.App
        +electronMain: ElectronMain
        --
        +whenReady()
        +onActivate()
        +onWindowAllClosed()
        +setupIPC()
        +broadcastAppReady()
    }

    ' 核心主进程服务
    class ElectronMain {
        -apiHandlers: Partial<MainProcessAPI>
        -settings: AppSettings
        -customWindows: Map<string, MainWindow>
        -activeWindow: BrowserWindow
        -updaterWindow: UpdaterWindow
        -updateTask: Map<string, Downloader>
        -errorPage: string
        --
        +constructor()
        +setupIPC()
        +registerAPI(handlers: Partial<MainProcessAPI>)
        +registerDefaultHandlers()
        +broadcast(event: string, data: any)
        +onRendererEvent(event: string, listener: Function)
        +openMainWindow(options: WindowFactoryOptions): string
        +createUpdaterWindow(): void
        +closeUpdaterWindow(): void
        +startDownloadTask(options: DownloadTaskOptions)
        +abortDownloadTask(taskId: string)
        +closeCustomWindow(windowId: string)
        +isUpdateAvailable(options: IsUpdateAvailableOptions)
        +getSystemInfo(): Promise<SystemInfo>
        +getAppVersion(): Promise<string>
        +minimizeWindow(): Promise<void>
        +maximizeWindow(): Promise<void>
        +closeWindow(): Promise<void>
    }

    ' 窗口基类
    abstract class BaseWindow {
        #window: BrowserWindow
        #windowId: string
        #config: BaseWindowConfig
        #ipcChannels: Set<string>
        --
        +constructor(windowId: string, config: BaseWindowConfig)
        +get win(): BrowserWindow
        +get id(): string
        +create(): BrowserWindow
        +show(): void
        +hide(): void
        +focus(): void
        +close(): void
        +isDestroyed(): boolean
        +openDevTools(): void
        +sendMessage(channel: string, ...args: any[]): void
        #setupEventListeners(): void
        #setupMenu(): void
        #loadContent(): void
        #handleDevelopmentMode(): void
        #handleWindowClosed(): void
        #registerIPCChannel(channel: string, handler: Function): void
        #setUserAgent(suffix: string): void
        --
        {abstract} #getWindowConfig(): BrowserWindowConstructorOptions
        {abstract} #getLoadOptions(): WindowLoadOptions
    }

    ' 主窗口类
    class MainWindow {
        -pack: string
        -windowData: WindowSizeData
        -unique: boolean
        --
        +constructor(options: WindowFactoryOptions)
        +getPackName(): string
        +getWindowData(): WindowSizeData
        --
        #getWindowConfig(): BrowserWindowConstructorOptions
        #getLoadOptions(): WindowLoadOptions
        #setupWindowSpecificIPC(): void
        -handleUniqueWindow(): void
    }

    ' 更新器窗口类
    class UpdaterWindow {
        -_dirname: string
        --
        +constructor(dirname: string)
        +start(): void
        --
        #getWindowConfig(): BrowserWindowConstructorOptions
        #getLoadOptions(): WindowLoadOptions
        #setupWindowSpecificIPC(): void
    }

    ' 下载器类
    class Downloader {
        -packageName: string
        -cachePath: string
        -url: string
        -md5: string
        -downloadTask: CancelableRequest<Response<Buffer>>
        +taskId: string
        --
        +constructor(options: DownloadOptions)
        +start(): Promise<void>
        +cancel(): boolean
        -checkMD5(filePath: string, md5: string): Promise<boolean>
        -bindListener(task: CancelableRequest): void
    }

    ' 菜单构建器类
    class MenuBuilder {
        +mainWindow: BrowserWindow
        --
        +constructor(mainWindow: BrowserWindow)
        +buildMenu(): Menu
        +setupDevelopmentEnvironment(): void
        +buildDarwinTemplate(): MenuItemConstructorOptions[]
        +buildDefaultTemplate(): MenuItemConstructorOptions[]
    }

    ' Electron原生类
    class BrowserWindow <<Electron Native>> {
        +webContents: WebContents
        +$$id$$: string
        +$$name$$: string
        --
        +loadURL(url: string): Promise<void>
        +loadFile(filePath: string): Promise<void>
        +close(): void
        +show(): void
        +hide(): void
        +focus(): void
        +isDestroyed(): boolean
        +minimize(): void
        +maximize(): void
        +unmaximize(): void
        +isMaximized(): boolean
        +setFullScreen(flag: boolean): void
        +isFullScreen(): boolean
    }
}

package "预加载脚本 (Preload)" as PreloadProcess {

    ' 预加载脚本入口
    class "preload/index.ts" as PreloadScript {
        +electronBridge: ElectronAPI
        +api: object
        --
        +createElectronBridge(): ElectronAPI
        +contextBridge.exposeInMainWorld(name: string, api: any)
        -setupEventListeners(): void
        -handleIPCEvents(): void
    }

    ' 预加载桥接实现 (内嵌在PreloadScript中)
    class PreloadBridge {
        -eventListeners: Map<string, Set<Function>>
        --
        +invoke<K>(method: K, ...args: Parameters): Promise<ReturnType>
        +on<K>(event: K, listener: Function): () => void
        +off<K>(event: K, listener?: Function): void
        +emit<K>(event: K, data: RendererProcessEvents[K]): void
        +once<K>(event: K, listener: Function): void
        -handleElectronEventBroadcast(eventData: IPCEvent): void
        -handleDownloadEvent(eventData: DownloadEvent): void
    }
}

package "渲染进程 (Renderer Process)" as RendererProcess {

    ' Vue应用入口
    class "App.vue" as VueApp {
        +systemInfo: Ref<SystemInfo>
        +appVersion: Ref<string>
        +settings: Ref<AppSettings>
        +notifications: Ref<string[]>
        +isLoading: Ref<boolean>
        +error: Ref<string>
        +tips: Ref<string>
        +electronClient: ElectronClient
        --
        +constructor()
        +getNovaVersion(): Promise<void>
        +doUpdateBundle(options: DownloadTaskOptions): Promise<void>
        +updateComplete(): Promise<void>
        +loadSystemInfo(): Promise<void>
        +loadSettings(): Promise<void>
        +setStatus(status: string): void
        +onMounted(): void
        +onUnmounted(): void
        -setupWindowControls(): object
        -setupEventListeners(): void
    }

    ' Electron客户端工具类
    class ElectronClient {
        -api: ElectronAPI
        -eventUnsubscribers: Map<string, () => void>
        --
        +constructor()
        +invoke<K>(method: K, ...args: Parameters): Promise<ReturnType>
        +onMainProcess<K>(event: K, callback: Function): () => void
        +emitToMainProcess<K>(event: K, data: RendererProcessEvents[K]): void
        +startDownloadTask(options: DownloadTaskOptions): DownloadTask
        +openMainWindow(options: WindowFactoryOptions): Promise<string>
        +isUpdateAvailable(options: IsUpdateAvailableOptions): Promise<IsUpdateAvailableResponse>
        +abortDownloadTask(taskId: string): Promise<void>
        +getSystemInfo(): Promise<SystemInfo>
        +getAppVersion(): Promise<string>
        +minimizeWindow(): Promise<void>
        +maximizeWindow(): Promise<void>
        +closeWindow(): Promise<void>
        +cleanup(): void
        +onThemeChange(callback: Function): () => void
        +onNotification(callback: Function): () => void
        +emitUserAction(action: string, data?: any): void
        +emitPageLoaded(url: string, title: string, loadTime: number): void
        +emitError(message: string, stack?: string, code?: string): void
        -downloadListener(): void
    }

    ' 下载任务类
    class DownloadTask {
        +taskId: string
        +client: ElectronClient
        --
        +constructor(taskId: string, client: ElectronClient)
        +onProgress(callback: (progress: DownloadProgress) => void): DownloadTask
        +onComplete(callback: (result: any) => void): DownloadTask
        +onError(callback: (error: Error) => void): DownloadTask
        +cancel(): Promise<void>
        -unbindListeners(): void
    }

    ' Vue应用主入口
    class "main.ts" as VueMain {
        --
        +createApp(App): App
        +mount(selector: string): void
    }
}

' 接口和类型定义
package "类型定义 (Types)" as TypesPackage {

    interface ElectronAPI {
        +invoke<K>(method: K, ...args: Parameters): Promise<ReturnType>
        +on<K>(event: K, listener: Function): () => void
        +off<K>(event: K, listener?: Function): void
        +emit<K>(event: K, data: RendererProcessEvents[K]): void
        +once<K>(event: K, listener: Function): void
    }

    interface MainProcessAPI {
        +getSystemInfo(): Promise<SystemInfo>
        +getAppVersion(): Promise<string>
        +minimizeWindow(): Promise<void>
        +maximizeWindow(): Promise<void>
        +closeWindow(): Promise<void>
        +getSettings(): Promise<AppSettings>
        +updateSettings(settings: Partial<AppSettings>): Promise<void>
        +getAppPath(): Promise<string>
        +getPackagePath(): Promise<string>
        +getAssetsPath(): Promise<string>
        +openMainWindow(options: WindowFactoryOptions): Promise<string>
        +closeCustomWindow(windowId: string): Promise<void>
        +startDownloadTask(options: DownloadTaskOptions): Promise<any>
        +abortDownloadTask(identity: string): Promise<void>
        +isUpdateAvailable(options: IsUpdateAvailableOptions): Promise<IsUpdateAvailableResponse>
        +decompressZip(options: object): Promise<boolean>
        +openDevTools(): Promise<void>
    }

    interface MainProcessEvents {
        +"app-ready": void
        +"app-will-quit": void
        +"system-theme-changed": string
        +"network-status-changed": NetworkStatus
        +"settings-updated": AppSettings
        +notification: NotificationData
        +"download-event": DownloadEvent
    }

    interface RendererProcessEvents {
        +"user-action": UserActionData
        +"page-loaded": PageLoadData
        +"error-occurred": ErrorData
        +"updater-open-main-window": object
    }

    interface BaseWindowConfig {
        +width: number
        +height: number
        +resizable: boolean
        +center: boolean
        +frame: boolean
        +transparent: boolean
        +autoHideMenuBar: boolean
        +title: string
        +webPreferences: Electron.WebPreferences
    }

    interface WindowLoadOptions {
        +url: string
        +file: string
        +query: Record<string, string>
    }

    interface WindowFactoryOptions {
        +pack: string
        +data: WindowSizeData
        +unique: boolean
        +needSystemInfo: boolean
    }

    interface WindowSizeData {
        +size: object
        +ratio: number
        +noFrame: boolean
        +transparent: boolean
        +remoteUrl: string
    }

    interface DownloadTaskOptions {
        +pack: string
        +url: string
        +md5: string
        +version: string
        +autoUnzip: boolean
        +checksum: boolean
        +taskId: string
    }

    interface DownloadOptions {
        +packageName: string
        +cachePath: string
        +url: string
        +md5: string
        +taskId: string
    }

    interface IsUpdateAvailableOptions {
        +url: string
        +pack: string
        +checkVersionOnly: boolean
    }

    interface IsUpdateAvailableResponse {
        +hasUpdate: boolean
        +localVersion: string
        +serverVersion: string
        +available: boolean
        +error: string
        +md5: string
        +url: string
    }

    interface DownloadProgress {
        +total: number
        +transferred: number
        +percent: number
    }

    interface DownloadEvent {
        +taskId: string
        +event: string
        +data: any
    }

    interface SystemInfo {
        +platform: string
        +arch: string
        +version: string
        +totalMemory: number
        +freeMemory: number
        +cpuCount: number
    }

    interface AppSettings {
        +theme: string
        +language: string
        +autoStart: boolean
        +notifications: boolean
    }
}

' Node.js和Electron原生类
class EventEmitter <<Node.js Native>> {
    +on(event: string, listener: Function): this
    +emit(event: string, ...args: any[]): boolean
    +removeAllListeners(event?: string): this
    +once(event: string, listener: Function): this
    +off(event: string, listener: Function): this
    +listenerCount(event: string): number
    +eventNames(): string[]
}

class Menu <<Electron Native>> {
    {static} +buildFromTemplate(template: MenuItemConstructorOptions[]): Menu
    {static} +setApplicationMenu(menu: Menu): void
    +popup(options?: PopupOptions): void
}

class WebContents <<Electron Native>> {
    +openDevTools(): void
    +reload(): void
    +send(channel: string, ...args: any[]): void
    +getUserAgent(): string
    +setUserAgent(userAgent: string): void
    +loadURL(url: string): Promise<void>
    +loadFile(filePath: string): Promise<void>
}

' ============================================================================
' 关系定义
' ============================================================================

' 继承关系 (Inheritance)
ElectronMain --|> EventEmitter : extends
ElectronClient --|> EventEmitter : extends
BaseWindow --|> EventEmitter : extends
Downloader --|> EventEmitter : extends
MainWindow --|> BaseWindow : extends
UpdaterWindow --|> BaseWindow : extends

' 实现关系 (Implementation)
PreloadBridge ..|> ElectronAPI : implements

' 组合关系 (Composition) - 强拥有关系
MainEntry *-- ElectronMain : creates & owns
ElectronMain *-- MainWindow : manages
ElectronMain *-- UpdaterWindow : manages
ElectronMain *-- Downloader : manages
BaseWindow *-- BrowserWindow : contains
BaseWindow *-- MenuBuilder : uses
ElectronClient *-- DownloadTask : creates
VueApp *-- ElectronClient : uses

' 聚合关系 (Aggregation) - 弱拥有关系
ElectronClient o-- ElectronAPI : uses
PreloadScript o-- PreloadBridge : contains

' 依赖关系 (Dependency) - 使用关系
VueApp ..> MainProcessAPI : uses interface
VueApp ..> WindowFactoryOptions : uses
VueApp ..> DownloadTaskOptions : uses
VueApp ..> SystemInfo : uses
VueApp ..> AppSettings : uses
ElectronClient ..> MainProcessAPI : calls methods
ElectronClient ..> MainProcessEvents : listens to
ElectronClient ..> RendererProcessEvents : emits
PreloadScript ..> ElectronMain : IPC communication
PreloadBridge ..> ElectronMain : IPC communication
DownloadTask ..> ElectronClient : callbacks to
MainWindow ..> WindowFactoryOptions : uses
MainWindow ..> WindowSizeData : uses
UpdaterWindow ..> BaseWindowConfig : uses
Downloader ..> DownloadOptions : uses
Downloader ..> DownloadProgress : emits
MenuBuilder ..> Menu : creates
MenuBuilder ..> BrowserWindow : operates on
BaseWindow ..> BaseWindowConfig : uses
BaseWindow ..> WindowLoadOptions : uses
BrowserWindow ..> WebContents : contains

' 关联关系 (Association)
ElectronMain -- MainProcessAPI : implements
ElectronMain -- MainProcessEvents : emits
ElectronClient -- RendererProcessEvents : emits
ElectronClient -- MainProcessEvents : receives

' ============================================================================
' 注释和说明
' ============================================================================

' 入口点标记
note top of MainEntry : 🚀 主进程入口点\n(main/index.ts)\n负责应用启动和生命周期管理
note top of VueApp : 🎨 渲染进程入口点\n(App.vue)\n用户界面和交互逻辑
note top of PreloadScript : 🌉 预加载脚本入口点\n(preload/index.ts)\n安全的IPC通信桥接
note top of ElectronClient : 📡 核心客户端工具\n封装所有主进程通信\n提供类型安全的API调用

' 架构说明
note as ArchNote
  <b>Electron三进程架构</b>

  <b>主进程 (Main Process):</b>
  - 应用生命周期管理
  - 窗口创建和管理
  - 系统资源访问
  - 文件下载和处理

  <b>渲染进程 (Renderer Process):</b>
  - Vue.js用户界面
  - 用户交互处理
  - 通过ElectronClient与主进程通信

  <b>预加载脚本 (Preload):</b>
  - 安全的IPC通信桥接
  - 类型安全的API暴露
  - 事件监听和转发
end note

' 设计模式说明
note as PatternNote
  <b>使用的设计模式</b>

  <b>观察者模式:</b>
  - EventEmitter基类
  - 事件驱动架构

  <b>工厂模式:</b>
  - 窗口创建和管理

  <b>桥接模式:</b>
  - PreloadBridge IPC通信

  <b>单例模式:</b>
  - ElectronMain实例
  - ElectronClient实例
end note

@enduml
