@startuml Electron应用类关系图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam classBackgroundColor #F8F9FA
skinparam classBorderColor #6C757D
skinparam classArrowColor #495057
skinparam packageBackgroundColor #E9ECEF
skinparam packageBorderColor #ADB5BD
skinparam stereotypeCBackgroundColor #FFF3CD
skinparam stereotypeCBorderColor #FFEAA7

title Electron应用类关系图 - 从electron-client入口开始

' 定义包和进程边界
package "主进程 (Main Process)" as MainProcess {
    
    ' 主进程入口
    class "main/index.ts" as MainEntry {
        +app: Electron.App
        +electronMain: ElectronMain
        --
        +whenReady()
        +onActivate()
        +onWindowAllClosed()
    }
    
    ' 核心主进程服务
    class ElectronMain {
        -apiHandlers: Partial<MainProcessAPI>
        -settings: AppSettings
        -customWindows: Map<string, MainWindow>
        -activeWindow: BrowserWindow
        -updaterWindow: UpdaterWindow
        -updateTask: Map<string, Downloader>
        --
        +constructor()
        +setupIPC()
        +registerAPI(handlers)
        +broadcast(event, data)
        +onRendererEvent(event, listener)
        +openMainWindow(options): string
        +createUpdaterWindow(): void
        +startDownloadTask(options)
        +closeCustomWindow(windowId)
    }
    
    ' 窗口基类
    abstract class BaseWindow {
        #window: BrowserWindow
        #windowId: string
        #config: BaseWindowConfig
        #ipcChannels: Set<string>
        --
        +constructor(windowId, config)
        +create(): BrowserWindow
        +show(): void
        +close(): void
        +isDestroyed(): boolean
        +sendMessage(channel, args)
        --
        {abstract} #getWindowConfig(): BrowserWindowConstructorOptions
        {abstract} #getLoadOptions(): WindowLoadOptions
        {abstract} #setupWindowSpecificIPC(): void
    }
    
    ' 主窗口类
    class MainWindow {
        -pack: string
        -windowData: WindowSizeData
        -unique: boolean
        --
        +constructor(options: WindowFactoryOptions)
        +getPackName(): string
        +getWindowData(): WindowSizeData
        --
        #getWindowConfig(): BrowserWindowConstructorOptions
        #getLoadOptions(): WindowLoadOptions
        #setupWindowSpecificIPC(): void
        -handleUniqueWindow(): void
    }
    
    ' 更新器窗口类
    class UpdaterWindow {
        -_dirname: string
        --
        +constructor(dirname: string)
        +start(): void
        --
        #getWindowConfig(): BrowserWindowConstructorOptions
        #getLoadOptions(): WindowLoadOptions
        #setupWindowSpecificIPC(): void
    }
    
    ' 下载器类
    class Downloader {
        -packageName: string
        -cachePath: string
        -url: string
        -md5: string
        -downloadTask: CancelableRequest
        +taskId: string
        --
        +constructor(options: DownloadOptions)
        +start(): Promise<void>
        +cancel(): boolean
        -checkMD5(filePath, md5): Promise<boolean>
        -bindListener(task): void
    }
    
    ' Electron原生类
    class BrowserWindow <<Electron Native>> {
        +webContents: WebContents
        +loadURL(url): Promise<void>
        +close(): void
        +isDestroyed(): boolean
        +show(): void
        +hide(): void
    }
}

package "预加载脚本 (Preload)" as PreloadProcess {
    
    ' 预加载脚本
    class "preload/index.ts" as PreloadScript {
        +electronBridge: ElectronAPI
        --
        +createElectronBridge(): ElectronAPI
        +contextBridge.exposeInMainWorld()
    }
    
    ' 预加载桥接实现
    class PreloadBridge {
        -eventListeners: Map<string, Set<Function>>
        --
        +invoke<K>(method, args): Promise<ReturnType>
        +on<K>(event, listener): () => void
        +off<K>(event, listener): void
        +emit<K>(event, data): void
        +once<K>(event, listener): void
    }
}

package "渲染进程 (Renderer Process)" as RendererProcess {
    
    ' Vue应用入口
    class "App.vue" as VueApp {
        +systemInfo: Ref<SystemInfo>
        +appVersion: Ref<string>
        +settings: Ref<AppSettings>
        +electronClient: ElectronClient
        --
        +getNovaVersion(): Promise<void>
        +doUpdateBundle(): Promise<void>
        +updateComplete(): Promise<void>
        +loadSystemInfo(): Promise<void>
        +loadSettings(): Promise<void>
    }
    
    ' Electron客户端工具类
    class ElectronClient {
        -api: ElectronAPI
        -eventUnsubscribers: Map<string, Function>
        --
        +constructor()
        +invoke<K>(method, args): Promise<ReturnType>
        +onMainProcess(event, callback): () => void
        +emitToMainProcess(event, data): void
        +startDownloadTask(options): DownloadTask
        +openMainWindow(options): Promise<string>
        +isUpdateAvailable(options): Promise<IsUpdateAvailableResponse>
        +cleanup(): void
    }
    
    ' 下载任务类
    class DownloadTask {
        +taskId: string
        +client: ElectronClient
        --
        +constructor(taskId, client)
        +onProgress(callback): DownloadTask
        +onComplete(callback): DownloadTask
        +onError(callback): DownloadTask
        +cancel(): Promise<void>
        -unbindListeners(): void
    }
}

' 接口和类型定义
package "类型定义 (Types)" as TypesPackage {
    
    interface ElectronAPI {
        +invoke<K>(method, args): Promise<ReturnType>
        +on<K>(event, listener): () => void
        +off<K>(event, listener): void
        +emit<K>(event, data): void
        +once<K>(event, listener): void
    }
    
    interface MainProcessAPI {
        +getSystemInfo(): Promise<SystemInfo>
        +openMainWindow(options): Promise<string>
        +startDownloadTask(options): Promise<any>
        +isUpdateAvailable(options): Promise<IsUpdateAvailableResponse>
        +closeWindow(): Promise<void>
        +minimizeWindow(): Promise<void>
        +maximizeWindow(): Promise<void>
    }
    
    interface WindowFactoryOptions {
        +pack: string
        +data: WindowSizeData
        +unique: boolean
    }
    
    interface DownloadTaskOptions {
        +pack: string
        +url: string
        +md5: string
        +version: string
        +taskId: string
    }
}

' Node.js原生类
class EventEmitter <<Node.js Native>> {
    +on(event, listener): this
    +emit(event, data): boolean
    +removeAllListeners(): this
    +once(event, listener): this
}

' 继承关系
ElectronMain --|> EventEmitter
ElectronClient --|> EventEmitter
BaseWindow --|> EventEmitter
Downloader --|> EventEmitter
MainWindow --|> BaseWindow
UpdaterWindow --|> BaseWindow

' 实现关系
PreloadBridge ..|> ElectronAPI : implements

' 组合关系
MainEntry *-- ElectronMain : creates
ElectronMain *-- MainWindow : manages
ElectronMain *-- UpdaterWindow : manages
ElectronMain *-- Downloader : manages
BaseWindow *-- BrowserWindow : contains
ElectronClient *-- ElectronAPI : uses
ElectronClient *-- DownloadTask : creates
VueApp *-- ElectronClient : uses

' 依赖关系
VueApp ..> MainProcessAPI : uses types
ElectronClient ..> MainProcessAPI : calls methods
PreloadScript ..> PreloadBridge : creates
PreloadBridge ..> ElectronMain : IPC communication
DownloadTask ..> ElectronClient : callbacks to
MainWindow ..> WindowFactoryOptions : uses
Downloader ..> DownloadTaskOptions : uses

' 入口点标记
note top of MainEntry : 主进程入口点\n(main/index.ts)
note top of VueApp : 渲染进程入口点\n(App.vue)
note top of PreloadScript : 预加载脚本入口点\n(preload/index.ts)

@enduml
