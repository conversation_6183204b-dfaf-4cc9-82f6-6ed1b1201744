import { BrowserWindow, screen } from 'electron'
import { BaseWindow, WindowLoadOptions } from './base-window'
import { WindowFactoryOptions, WindowSizeData } from '../method.types'
import { DEBUG } from '../env'
import { MAIN_WINDOW_SIZE, PROXY, WINDOW_ADAPTER } from './electron-main'
import { join } from 'path'

/**
 * 主窗口类 - 负责创建和管理主应用窗口
 *
 * 继承自BaseWindow，复用通用窗口管理功能。
 * 主要特性：
 * - 支持动态窗口尺寸计算
 * - 支持唯一窗口模式
 * - 支持远程URL加载
 * - 自动设置用户代理
 */
export class MainWindow extends BaseWindow {
	private pack: string
	private windowData: WindowSizeData
	private unique: boolean

	constructor(options: WindowFactoryOptions) {
		const { pack, data = {}, unique = false } = options

		// 计算窗口尺寸
		const { width, height } = data.size || MAIN_WINDOW_SIZE
		const screenSize = screen.getPrimaryDisplay().size
		let ratio = Math.min(screenSize.width / width, screenSize.height / height)
		ratio *= data.ratio || 1

		const finalWidth = (width * ratio) | 0
		const finalHeight = (height * ratio) | 0

		// 调用基类构造函数
		super(`${pack}-${Date.now()}`, {
			width: finalWidth,
			height: finalHeight,
			resizable: true,
			center: true,
			frame: !data.noFrame,
			transparent: !!data.transparent,
			autoHideMenuBar: true,
			title: '豆神王者Club'
		})

		this.pack = pack
		this.windowData = data
		this.unique = unique
	}

	/**
	 * 实现抽象方法：获取窗口配置
	 */
	protected getWindowConfig(): Electron.BrowserWindowConstructorOptions {
		return {
			title: this.config.title,
			width: this.config.width,
			height: this.config.height,
			resizable: this.config.resizable,
			center: this.config.center,
			frame: this.config.frame,
			transparent: this.config.transparent,
			autoHideMenuBar: this.config.autoHideMenuBar,
			webPreferences: {
				webSecurity: false,
				nodeIntegration: true,
				contextIsolation: false,
				preload: join(__dirname, '../preload/', 'index.js'),
				...this.config.webPreferences
			}
		}
	}

	/**
	 * 实现抽象方法：获取加载选项
	 */
	protected getLoadOptions(): WindowLoadOptions {
		// 确定加载的URL
		let url: string

		if (DEBUG && WINDOW_ADAPTER[this.pack]) {
			url = WINDOW_ADAPTER[this.pack]
		} else {
			url = `${PROXY}://${this.pack}${DEBUG ? '?env=test' : ''}`
		}

		if (this.windowData.remoteUrl) {
			url = this.windowData.remoteUrl
		}

		return { url }
	}

	/**
	 * 实现抽象方法：设置窗口特定的IPC处理器
	 */
	protected setupWindowSpecificIPC(): void {
		// 主窗口可以根据需要添加特定的IPC处理逻辑
		// 目前使用ElectronMain的通用IPC处理器
	}

	/**
	 * 创建窗口前的预处理
	 */
	public create(): BrowserWindow {
		// 处理唯一窗口逻辑
		if (this.unique) {
			this.handleUniqueWindow()
		}

		// 调用基类的create方法
		const window = super.create()

		// 设置窗口标识
		;(window as any).$$name$$ = this.pack

		// 设置用户代理
		this.setUserAgent(this.pack)

		return window
	}

	/**
	 * 处理唯一窗口逻辑
	 */
	private handleUniqueWindow(): void {
		BrowserWindow.getAllWindows().forEach((win: any) => {
			if (win.$$name$$ === this.pack) {
				win.close()
			}
		})
	}

	/**
	 * 获取窗口包名
	 */
	public getPackName(): string {
		return this.pack
	}

	/**
	 * 获取窗口数据
	 */
	public getWindowData(): WindowSizeData {
		return this.windowData
	}
}
