import { BaseWindow, WindowLoadOptions } from './base-window'
import { join } from 'path'
import { is } from '@electron-toolkit/utils'

/**
 * 更新器窗口类 - 负责创建和管理更新窗口
 *
 * 继承自BaseWindow，复用通用窗口管理功能。
 * 主要特性：
 * - 无边框窗口设计
 * - 固定尺寸，不可调整
 * - 处理与主窗口的通信
 * - 在开发模式下默认不打开开发者工具
 */
export default class UpdaterWindow extends BaseWindow {
	// 保留 dirname 参数以维持 API 兼容性，但当前实现中未使用
	// @ts-ignore: kept for API compatibility
	private readonly _dirname: string

	constructor(dirname: string) {
		// 调用基类构造函数，设置窗口ID和基本配置
		super('updater-window', {
			width: 800,
			height: 600,
			resizable: false,
			center: true,
			frame: false,
			autoHideMenuBar: true,
			title: '更新器'
		})
		this._dirname = dirname
	}

	/**
	 * 实现抽象方法：获取窗口配置
	 */
	protected getWindowConfig(): Electron.BrowserWindowConstructorOptions {
		return {
			width: this.config.width,
			height: this.config.height,
			resizable: this.config.resizable,
			center: this.config.center,
			frame: this.config.frame,
			autoHideMenuBar: this.config.autoHideMenuBar,
			title: this.config.title,
			webPreferences: {
				preload: join(__dirname, '../preload/index.js'),
				sandbox: false,
				...this.config.webPreferences
			}
		}
	}

	/**
	 * 实现抽象方法：获取加载选项
	 */
	protected getLoadOptions(): WindowLoadOptions {
		// 使用与 main/index.ts 相同的逻辑来加载页面
		if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
			return { url: process.env['ELECTRON_RENDERER_URL'] }
		} else {
			return { file: join(__dirname, '../renderer/index.html') }
		}
	}

	/**
	 * 启动更新器窗口
	 */
	start(): void {
		// 使用基类的create方法创建窗口
		this.create()
	}

	/**
	 * 实现抽象方法：设置窗口特定的IPC处理器
	 */
	protected setupWindowSpecificIPC(): void {
		if (!this.window) return

		// 监听来自更新器窗口的 openMainWindow 请求
		const handleOpenMainWindow = (_event: any, options: { pack: string; data: any }) => {
			// 发射事件给外部监听器（保持向后兼容）
			this.emit('open-main-window', options)
		}

		// 设置 IPC 监听器
		const channelName = `updater-open-main-window`
		this.registerIPCChannel(channelName, handleOpenMainWindow)

		// 向更新器窗口发送通道名称，让它知道如何与主进程通信
		this.window.webContents.once('did-finish-load', () => {
			if (this.window && !this.window.isDestroyed()) {
				this.window.webContents.send('updater-ipc-channel', channelName)
			}
		})
	}

	/**
	 * 重写开发模式处理，更新器窗口默认不打开开发者工具
	 */
	protected handleDevelopmentMode(): void {
		// 更新器窗口在开发模式下默认不打开开发者工具
		// 如果需要调试，可以手动调用 openDevTools()
	}
}
