import { BrowserWindow, ipcMain, app } from 'electron'
import { EventEmitter } from 'events'
import MenuBuilder from './menu'
import { DEBUG, TEST } from '../env'

/**
 * 窗口配置接口
 */
export interface BaseWindowConfig {
	width: number
	height: number
	resizable?: boolean
	center?: boolean
	frame?: boolean
	transparent?: boolean
	autoHideMenuBar?: boolean
	title?: string
	webPreferences?: Electron.WebPreferences
}

/**
 * 窗口加载选项接口
 */
export interface WindowLoadOptions {
	url?: string
	file?: string
	query?: Record<string, string>
}

/**
 * 抽象窗口基类
 *
 * 这个基类提供了Electron窗口管理的通用功能和接口，包括：
 * - 窗口创建和配置
 * - 事件监听器管理
 * - IPC通信处理
 * - 生命周期管理
 * - 菜单设置
 *
 * 子类需要实现抽象方法来提供具体的窗口配置和行为。
 */
export abstract class BaseWindow extends EventEmitter {
	protected window: BrowserWindow | null = null
	protected windowId: string
	protected config: BaseWindowConfig
	protected ipcChannels: Set<string> = new Set()

	constructor(windowId: string, config: BaseWindowConfig) {
		super()
		this.windowId = windowId
		this.config = config
	}

	/**
	 * 获取窗口实例
	 */
	get win(): BrowserWindow | null {
		return this.window
	}

	/**
	 * 获取窗口ID
	 */
	get id(): string {
		return this.windowId
	}

	/**
	 * 抽象方法：获取窗口配置
	 * 子类必须实现此方法来提供具体的窗口配置
	 */
	protected abstract getWindowConfig(): Electron.BrowserWindowConstructorOptions

	/**
	 * 抽象方法：获取加载选项
	 * 子类必须实现此方法来指定如何加载窗口内容
	 */
	protected abstract getLoadOptions(): WindowLoadOptions

	/**
	 * 创建窗口
	 */
	public create(): BrowserWindow {
		if (this.window && !this.window.isDestroyed()) {
			return this.window
		}

		// 获取窗口配置
		const windowConfig = this.getWindowConfig()

		// 创建窗口
		this.window = new BrowserWindow(windowConfig)

		// 设置窗口标识
		;(this.window as any).$$id$$ = this.windowId

		// 设置事件监听器
		this.setupEventListeners()

		// 设置菜单
		this.setupMenu()

		// 加载窗口内容
		this.loadContent()

		// 开发模式下的特殊处理
		this.handleDevelopmentMode()

		return this.window
	}

	/**
	 * 设置窗口事件监听器
	 */
	protected setupEventListeners(): void {
		if (!this.window) return

		// 窗口关闭事件
		this.window.on('closed', () => {
			this.handleWindowClosed()
		})

		// 窗口准备就绪事件
		this.window.on('ready-to-show', () => {
			this.emit('window-ready')
		})

		// 页面加载完成事件
		this.window.webContents.once('did-finish-load', () => {
			this.emit('window-loaded')
		})

		// 页面加载失败事件
		this.window.webContents.on('did-fail-load', (_event, errorCode, errorDescription) => {
			console.error(`Window ${this.windowId} failed to load:`, errorCode, errorDescription)
			this.emit('window-load-failed', { errorCode, errorDescription })
		})
	}

	/**
	 * 设置窗口菜单
	 */
	protected setupMenu(): void {
		if (!this.window) return

		const menuBuilder = new MenuBuilder(this.window)
		menuBuilder.buildMenu()
	}

	/**
	 * 加载窗口内容
	 */
	protected loadContent(): void {
		if (!this.window) return

		const loadOptions = this.getLoadOptions()

		if (loadOptions.url) {
			// 加载URL
			let url = loadOptions.url
			if (loadOptions.query) {
				const queryString = new URLSearchParams(loadOptions.query).toString()
				url += (url.includes('?') ? '&' : '?') + queryString
			}
			this.window.loadURL(url)
		} else if (loadOptions.file) {
			// 加载本地文件
			this.window.loadFile(loadOptions.file)
		} else {
			throw new Error(`Window ${this.windowId}: No load options specified`)
		}
	}

	/**
	 * 开发模式下的特殊处理
	 */
	protected handleDevelopmentMode(): void {
		if (!this.window) return

		// 在开发或测试模式下打开开发者工具
		if (DEBUG || TEST) {
			// 可以在子类中重写此行为
			this.openDevTools()
		}
	}

	/**
	 * 处理窗口关闭
	 */
	protected handleWindowClosed(): void {
		console.log(`Window ${this.windowId} closed`)
		this.window = null
		this.emit('window-closed')
	}

	/**
	 * 注册IPC通道
	 */
	protected registerIPCChannel(channel: string, handler: (...args: any[]) => void): void {
		ipcMain.on(channel, handler)
		this.ipcChannels.add(channel)
	}

	/**
	 * 显示窗口
	 */
	public show(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.show()
		}
	}

	/**
	 * 隐藏窗口
	 */
	public hide(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.hide()
		}
	}

	/**
	 * 聚焦窗口
	 */
	public focus(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.focus()
		}
	}

	/**
	 * 关闭窗口
	 */
	public close(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.close()
		}
	}

	/**
	 * 检查窗口是否已销毁
	 */
	public isDestroyed(): boolean {
		return !this.window || this.window.isDestroyed()
	}

	/**
	 * 打开开发者工具
	 */
	public openDevTools(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.webContents.openDevTools()
		}
	}

	/**
	 * 向窗口发送消息
	 */
	public sendMessage(channel: string, ...args: any[]): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.webContents.send(channel, ...args)
		}
	}

	/**
	 * 设置窗口用户代理
	 */
	protected setUserAgent(suffix: string): void {
		if (!this.window) return

		const userAgent = this.window.webContents.getUserAgent()
		this.window.webContents.setUserAgent(`${userAgent} KCPC v${app.getVersion()} ${suffix}`)
	}
}
