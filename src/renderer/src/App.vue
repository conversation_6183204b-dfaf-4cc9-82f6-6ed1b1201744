<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { electronClient } from './utils/electron-client'
import {
	SystemInfo,
	AppSettings,
	WindowFactoryOptions,
	IsUpdateAvailableResponse,
	DownloadTaskOptions
} from '../../method.types'
import { UPDATE } from './lib/types'
import type { DownloadProgress } from '../../core/downloader'

// 响应式数据
const systemInfo = ref<SystemInfo | null>(null)
const appVersion = ref<string>('')
const settings = ref<AppSettings | null>(null)
const notifications = ref<string[]>([])
const isLoading = ref(false)

const error = ref('')
const tips = ref('')

// 新的 API 调用示例
const loadSystemInfo = async () => {
	try {
		isLoading.value = true
		systemInfo.value = await electronClient.getSystemInfo()
		appVersion.value = await electronClient.getAppVersion()
	} catch (error) {
		console.error('加载系统信息失败:', error)
		notifications.value.push(`错误: ${error.message}`)
	} finally {
		isLoading.value = false
	}
}

const getNovaVersion = () => {
	// const url = import.meta.env.VITE_APP_BUNDLE_URL
	const url = 'https://probe-1318590712.cos.ap-beijing.myqcloud.com/pc-nova/bundles' // TODO 此处为调试path，后续需要添加.env.{runtime}文件进行区分
	let request = {
		url: `${url}/nova_ui.json`,
		pack: 'main-ui'
	}
	electronClient
		.isUpdateAvailable(request)
		.then(async (result: IsUpdateAvailableResponse) => {
			console.log('检查基础框架版本成功', result)
			if (result.hasUpdate) {
				// TODO 显示更新
				doUpdateBundle(result, url)
			} else {
				setStatus(UPDATE.LASTEST)
				console.log('check update success, update complete')
				updateComplete()
			}
		})
		.catch((err: any) => {
			console.error('检测基础框架是否有更新出错', err)
			// error.value = err.message || err
			setStatus(UPDATE.ERROR)
		})
}

const doUpdateBundle = async (
	new_pack_info: IsUpdateAvailableResponse,
	url: string
): Promise<void> => {
	setStatus(UPDATE.DOWNLOADING_UI)
	const request: DownloadTaskOptions = {
		autoUnzip: true,
		checksum: true,
		md5: new_pack_info.md5,
		pack: 'main-ui',
		url: `${url}/${new_pack_info.url}`,
		version: new_pack_info.serverVersion
	}
	const task = await electronClient.startDownloadTask(request)

	task.on('progress', (progress: DownloadProgress) => {
		console.log('progress', progress)
	})
	task.on('error', (error: any) => {
		console.error(error)
	})
	task.on('complete', () => {
		console.log('complete')
	})

	console.log('启动更新task------- 请求参数', new_pack_info, '任务：', task)
}

const updateComplete = async () => {
	setTimeout(async () => {
		// electronClient.getUpdaterWindow()
		electronClient.emit('updater-open-main-window', { action: 'o' })
		const options: WindowFactoryOptions = {
			pack: 'main-ui',
			data: {
				size: {
					width: 2326,
					height: 1310
				},
				ratio: 0.92
			}
		}
		await electronClient.openMainWindow(options).catch((err) => {
			console.log('开启主框架窗口出错', err)
		})
	}, 3000)
}

const setStatus = (status: string) => {
	switch (status) {
		case UPDATE.LASTEST:
			tips.value = '已是最新版本'
			break
		case UPDATE.AVAILABLE:
			tips.value = '发现新版本'
			break
		case UPDATE.CHECKING:
			tips.value = '正在检查新版本...'
			break
		case UPDATE.ERROR:
			tips.value = `更新出错，错误信息:${error.value}`
			break
		case UPDATE.DOWNLOADING:
			tips.value = '正在下载新版本...'
			break
		case UPDATE.DOWNLOADED:
			tips.value = '下载完成，请等待安装...'
			break
		case UPDATE.DOWNLOADING_UI:
			tips.value = '正在下载基础包...'
			break
		case UPDATE.DOWNLOADED_UI:
			tips.value = '下载完成，请等待解压...'
			break
		default:
			tips.value = '正在检查版本更新...'
	}
}

const loadSettings = async () => {
	try {
		settings.value = await electronClient.getSettings()
	} catch (error) {
		console.error('加载设置失败:', error)
		notifications.value.push(`错误: ${error.message}`)
	}
}

const windowControls = {
	minimize: () => electronClient.minimizeWindow(),
	maximize: () => electronClient.maximizeWindow(),
	close: () => electronClient.closeWindow()
}

// 事件监听器
const eventUnsubscribers: (() => void)[] = []

onMounted(() => {
	// 加载初始数据
	loadSystemInfo()
	loadSettings()

	// 发送页面加载事件
	electronClient.emitPageLoaded(window.location.href, document.title, performance.now())
})

onUnmounted(() => {
	// 清理事件监听器
	eventUnsubscribers.forEach((unsubscribe) => unsubscribe())
	electronClient.cleanup()
})
</script>

<template>
	<div class="app">
		<header class="header">
			<img alt="logo" class="logo" src="./assets/electron.svg" />
			<div class="window-controls">
				<button class="control-btn" @click="windowControls.minimize">−</button>
				<button class="control-btn" @click="windowControls.maximize">□</button>
				<button class="control-btn" @click="windowControls.close">×</button>
			</div>
		</header>

		<main class="main">
			<section class="section">
				<h2>检查更新</h2>
				<div class="info-grid">
					<div class="info-item">
						<label>检查结果</label>
						<span>{{ tips }}</span>
					</div>
				</div>
				<button class="btn" @click="getNovaVersion">业务层检查更新</button>
			</section>

			<!-- 通知区域 -->
			<section class="section">
				<h2>事件通知</h2>
				<div class="notifications">
					<div
						v-for="(notification, index) in notifications.slice(-5)"
						:key="index"
						class="notification"
					>
						{{ notification }}
					</div>
					<div v-if="notifications.length === 0" class="no-notifications">暂无通知</div>
				</div>
				<button class="btn btn-small" @click="notifications = []">清空通知</button>
			</section>
		</main>

		<footer class="footer">
			<Versions />
			<p class="tip">按 <code>F12</code> 打开开发者工具查看控制台日志</p>
		</footer>
	</div>
</template>

<style scoped>
.app {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30px;
	padding: 20px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 12px;
	color: white;
}

.header h1 {
	margin: 0;
	font-size: 24px;
	font-weight: 600;
}

.logo {
	height: 60px;
	width: 60px;
	will-change: filter;
	transition: filter 300ms;
}

.logo:hover {
	filter: drop-shadow(0 0 2em #646cffaa);
}

.window-controls {
	display: flex;
	gap: 8px;
}

.control-btn {
	width: 32px;
	height: 32px;
	border: none;
	border-radius: 6px;
	background: rgba(255, 255, 255, 0.2);
	color: white;
	cursor: pointer;
	font-size: 16px;
	font-weight: bold;
	transition: background-color 0.2s;
}

.control-btn:hover {
	background: rgba(255, 255, 255, 0.3);
}

.main {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
	gap: 20px;
	margin-bottom: 30px;
	overflow: auto;
}

.section {
	background: white;
	border-radius: 12px;
	padding: 24px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	border: 1px solid #e5e7eb;
}

.section h2 {
	margin: 0 0 20px 0;
	font-size: 20px;
	font-weight: 600;
	color: #374151;
	border-bottom: 2px solid #e5e7eb;
	padding-bottom: 10px;
}

.loading {
	text-align: center;
	color: #6b7280;
	font-style: italic;
}

.info-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 12px;
	margin-bottom: 20px;
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px;
	background: #f9fafb;
	border-radius: 8px;
	border: 1px solid #e5e7eb;
}

.info-item label {
	font-weight: 600;
	color: #374151;
}

.info-item span {
	color: #6b7280;
	font-family: 'Monaco', 'Menlo', monospace;
	font-size: 14px;
}

.settings {
	margin-bottom: 20px;
}

.setting-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 0;
	border-bottom: 1px solid #e5e7eb;
}

.setting-item:last-child {
	border-bottom: none;
}

.setting-item label {
	font-weight: 600;
	color: #374151;
	flex: 1;
}

.setting-item span {
	color: #6b7280;
	margin-right: 12px;
}

.file-actions {
	margin-bottom: 20px;
}

.btn {
	background: #3b82f6;
	color: white;
	border: none;
	padding: 12px 24px;
	border-radius: 8px;
	cursor: pointer;
	font-size: 14px;
	font-weight: 600;
	transition: all 0.2s;
	margin-right: 8px;
	margin-bottom: 8px;
}

.btn:hover {
	background: #2563eb;
	transform: translateY(-1px);
	box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.btn-small {
	padding: 8px 16px;
	font-size: 12px;
}

.notifications {
	max-height: 200px;
	overflow-y: auto;
	margin-bottom: 16px;
}

.notification {
	padding: 12px;
	margin-bottom: 8px;
	background: #f0f9ff;
	border: 1px solid #bae6fd;
	border-radius: 8px;
	color: #0c4a6e;
	font-size: 14px;
}

.no-notifications {
	text-align: center;
	color: #6b7280;
	font-style: italic;
	padding: 20px;
}

.footer {
	text-align: center;
	padding: 20px;
	border-top: 1px solid #e5e7eb;
	background: #f9fafb;
	border-radius: 12px;
}

.tip {
	margin: 16px 0 0 0;
	color: #6b7280;
	font-size: 14px;
}

.tip code {
	background: #e5e7eb;
	padding: 2px 6px;
	border-radius: 4px;
	font-family: 'Monaco', 'Menlo', monospace;
	font-size: 12px;
}
</style>
